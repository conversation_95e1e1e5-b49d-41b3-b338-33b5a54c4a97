<!DOCTYPE html>
<html>
<head>
    <title>设备定位展示</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: #f5f8fa;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 100%;
            height: 100vh;
            background: #fff;
            display: flex;
            flex-direction: column;
        }
        .header {
            background-color: #34495e;
            color: white;
            padding: 12px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .back-link {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            transition: background 0.3s;
        }
        .back-link:hover {
            background: rgba(255,255,255,0.3);
        }
        .control-panel {
            background: #fff;
            padding: 16px 24px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            gap: 16px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        .btn-primary {
            background: #3498db;
            color: white;
        }
        .btn-primary:hover {
            background: #2980b9;
        }
        .map-container {
            flex: 1;
            position: relative;
            overflow: hidden;
        }
        #mapDiv {
            width: 100%;
            height: 100%;
            background: #f0f0f0;
        }
        .info-panel {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255,255,255,0.95);
            padding: 12px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            min-width: 250px;
            z-index: 1000;
        }
        .info-panel h4 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 14px;
        }
        .info-item {
            margin-bottom: 6px;
            font-size: 13px;
            color: #666;
        }
        .error-message {
            color: #e74c3c;
            background: #fdf2f2;
            padding: 8px 12px;
            border-radius: 4px;
            border-left: 4px solid #e74c3c;
            margin: 8px 0;
        }
        .success-message {
            color: #27ae60;
            background: #f0f9f4;
            padding: 8px 12px;
            border-radius: 4px;
            border-left: 4px solid #27ae60;
            margin: 8px 0;
        }
        .no-map {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #666;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>设备定位展示</h1>
            <a class="back-link" href="/">← 返回主页</a>
        </div>

        <div class="control-panel">
            <button class="btn btn-primary" onclick="refreshLocation()">刷新位置</button>
            <span id="statusText">正在获取最新位置数据...</span>
            <div id="messageArea"></div>
        </div>

        <div class="map-container">
            <div id="mapDiv">
                <div class="no-map">
                    <div>
                        <h3>设备位置信息</h3>
                        <p>由于地图API加载问题，暂时使用文本方式显示位置信息</p>
                        <div id="locationInfo">正在获取位置数据...</div>
                    </div>
                </div>
            </div>
            <div class="info-panel" id="infoPanel">
                <h4>最新位置信息</h4>
                <div id="deviceInfo">
                    <div class="info-item">状态: <span id="deviceStatus">等待数据</span></div>
                    <div class="info-item">经度: <span id="longitude">-</span></div>
                    <div class="info-item">纬度: <span id="latitude">-</span></div>
                    <div class="info-item">时间: <span id="updateTime">-</span></div>
                    <div class="info-item">GPS状态: <span id="gpsStatus">-</span></div>
                    <div class="info-item">ICCID: <span id="iccid">-</span></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let updateInterval;

        // 获取最新位置数据
        async function getLatestLocation() {
            try {
                const response = await fetch('/api/latest_location');
                const result = await response.json();

                if (result.status === 'success' && result.data) {
                    updateLocationDisplay(result.data);
                    document.getElementById('statusText').textContent = '位置数据已更新';
                } else {
                    document.getElementById('statusText').textContent = '暂无位置数据';
                    clearLocationDisplay();
                }
            } catch (error) {
                console.error('获取位置数据失败:', error);
                document.getElementById('statusText').textContent = '获取位置数据失败';
                showMessage('获取位置数据失败', 'error');
            }
        }

        // 更新位置显示
        function updateLocationDisplay(data) {
            document.getElementById('deviceStatus').textContent = '在线';
            document.getElementById('longitude').textContent = data.longitude || '-';
            document.getElementById('latitude').textContent = data.latitude || '-';
            document.getElementById('updateTime').textContent = data.time || '-';
            document.getElementById('gpsStatus').textContent = data.gps_status || '-';
            document.getElementById('iccid').textContent = data.iccid || '-';

            // 更新地图区域的位置信息
            const locationInfo = document.getElementById('locationInfo');
            if (data.longitude && data.latitude) {
                locationInfo.innerHTML = `
                    <div style="text-align: left; line-height: 1.6;">
                        <p><strong>经度:</strong> ${data.longitude}</p>
                        <p><strong>纬度:</strong> ${data.latitude}</p>
                        <p><strong>时间:</strong> ${data.time}</p>
                        <p><strong>GPS状态:</strong> ${data.gps_status}</p>
                        <p><strong>ICCID:</strong> ${data.iccid}</p>
                        <p><strong>百度地图链接:</strong></p>
                        <a href="https://api.map.baidu.com/marker?location=${data.latitude},${data.longitude}&title=设备位置&content=ICCID:${data.iccid}&output=html" target="_blank" style="color: #3498db;">在百度地图中查看</a>
                        <p><strong>高德地图链接:</strong></p>
                        <a href="https://uri.amap.com/marker?position=${data.longitude},${data.latitude}&name=设备位置" target="_blank" style="color: #3498db;">在高德地图中查看</a>
                    </div>
                `;
            } else {
                locationInfo.textContent = '暂无有效的位置数据';
            }
        }

        // 清除位置显示
        function clearLocationDisplay() {
            document.getElementById('deviceStatus').textContent = '离线';
            document.getElementById('longitude').textContent = '-';
            document.getElementById('latitude').textContent = '-';
            document.getElementById('updateTime').textContent = '-';
            document.getElementById('gpsStatus').textContent = '-';
            document.getElementById('iccid').textContent = '-';
            document.getElementById('locationInfo').textContent = '暂无位置数据';
        }

        // 手动刷新位置
        function refreshLocation() {
            document.getElementById('statusText').textContent = '正在刷新位置数据...';
            getLatestLocation();
        }

        // 启动自动更新
        function startAutoUpdate() {
            // 立即获取一次数据
            getLatestLocation();

            // 每5秒自动更新一次
            updateInterval = setInterval(getLatestLocation, 5000);
        }

        // 停止自动更新
        function stopAutoUpdate() {
            if (updateInterval) {
                clearInterval(updateInterval);
                updateInterval = null;
            }
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            const messageArea = document.getElementById('messageArea');
            const className = type === 'error' ? 'error-message' : 'success-message';
            messageArea.innerHTML = `<div class="${className}">${message}</div>`;

            // 3秒后自动清除消息
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 3000);
        }

        // 页面加载完成后启动自动更新
        window.onload = function() {
            startAutoUpdate();
        };

        // 页面关闭时停止自动更新
        window.addEventListener('beforeunload', function() {
            stopAutoUpdate();
        });
    </script>
</body>
</html>
