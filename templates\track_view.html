<!DOCTYPE html>
<html>
<head>
    <title>设备轨迹展示</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: #f5f8fa;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 100%;
            height: 100vh;
            background: #fff;
            display: flex;
            flex-direction: column;
        }
        .header {
            background-color: #34495e;
            color: white;
            padding: 12px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .back-link {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            transition: background 0.3s;
        }
        .back-link:hover {
            background: rgba(255,255,255,0.3);
        }
        .control-panel {
            background: #fff;
            padding: 16px 24px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            gap: 16px;
            flex-wrap: wrap;
        }
        .input-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .input-group label {
            font-weight: bold;
            color: #333;
            min-width: 80px;
        }
        .input-group input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            width: 200px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        .btn-primary {
            background: #3498db;
            color: white;
        }
        .btn-primary:hover {
            background: #2980b9;
        }
        .btn-success {
            background: #27ae60;
            color: white;
        }
        .btn-success:hover {
            background: #229954;
        }
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        .btn-warning:hover {
            background: #e67e22;
        }
        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        .map-container {
            flex: 1;
            position: relative;
            overflow: hidden;
        }
        #mapDiv {
            width: 100%;
            height: 100%;
        }
        .info-panel {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255,255,255,0.95);
            padding: 12px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            min-width: 200px;
            max-width: 300px;
            z-index: 1000;
        }
        .info-panel h4 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 14px;
        }
        .info-item {
            margin-bottom: 4px;
            font-size: 12px;
            color: #666;
        }
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255,255,255,0.9);
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            z-index: 1001;
        }
        .error-message {
            color: #e74c3c;
            background: #fdf2f2;
            padding: 8px 12px;
            border-radius: 4px;
            border-left: 4px solid #e74c3c;
            margin: 8px 0;
        }
        .success-message {
            color: #27ae60;
            background: #f0f9f4;
            padding: 8px 12px;
            border-radius: 4px;
            border-left: 4px solid #27ae60;
            margin: 8px 0;
        }
    </style>
    <!-- 引入天地图API -->
    <script type="text/javascript" src="https://api.tianditu.gov.cn/api?v=4.0&tk=请在这里填入您的天地图API密钥"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>设备轨迹展示</h1>
            <a class="back-link" href="/">← 返回主页</a>
        </div>

        <div class="control-panel">
            <div class="input-group">
                <label for="ccidInput">CCID号:</label>
                <input type="text" id="ccidInput" placeholder="输入设备CCID号">
            </div>
            <button class="btn btn-primary" onclick="loadTrackData()">加载轨迹</button>
            <button class="btn btn-success" id="playBtn" onclick="playTrack()" disabled>播放</button>
            <button class="btn btn-warning" id="pauseBtn" onclick="pauseTrack()" disabled>暂停</button>
            <button class="btn" onclick="resetTrack()">重置</button>
            <div id="messageArea"></div>
        </div>

        <div class="map-container">
            <div id="mapDiv"></div>
            <div class="info-panel" id="infoPanel" style="display: none;">
                <h4>轨迹信息</h4>
                <div id="trackInfo"></div>
            </div>
            <div class="loading" id="loadingDiv" style="display: none;">
                <div>正在加载轨迹数据...</div>
            </div>
        </div>
    </div>

    <script>
        let map;
        let trackData = [];
        let trackLine;
        let markers = [];
        let currentIndex = 0;
        let playInterval;
        let isPlaying = false;

        // 初始化地图
        function initMap() {
            // 创建地图实例
            map = new T.Map('mapDiv');

            // 设置地图中心点（北京）
            const center = new T.LngLat(116.40969, 39.89945);
            map.centerAndZoom(center, 10);

            // 添加地图类型控件
            const ctrl = new T.Control.MapType();
            map.addControl(ctrl);

            // 添加缩放控件
            const zoomCtrl = new T.Control.Zoom();
            map.addControl(zoomCtrl);
        }

        // 加载轨迹数据
        async function loadTrackData() {
            const ccid = document.getElementById('ccidInput').value.trim();
            if (!ccid) {
                showMessage('请输入CCID号', 'error');
                return;
            }

            showLoading(true);
            clearTrack();

            try {
                const response = await fetch(`/api/track_data?ccid=${encodeURIComponent(ccid)}`);
                const result = await response.json();

                if (result.status === 'success' && result.data.length > 0) {
                    trackData = result.data;
                    showTrackOnMap();
                    showMessage(`成功加载 ${trackData.length} 个轨迹点`, 'success');
                    document.getElementById('playBtn').disabled = false;
                } else {
                    showMessage('未找到该设备的轨迹数据', 'error');
                }
            } catch (error) {
                console.error('加载轨迹数据失败:', error);
                showMessage('加载轨迹数据失败', 'error');
            } finally {
                showLoading(false);
            }
        }

        // 在地图上显示轨迹
        function showTrackOnMap() {
            if (trackData.length === 0) return;

            // 创建轨迹点数组
            const points = trackData.map(point => new T.LngLat(point.longitude, point.latitude));

            // 创建轨迹线
            trackLine = new T.Polyline(points, {
                color: '#3498db',
                weight: 3,
                opacity: 0.8
            });
            map.addOverLay(trackLine);

            // 添加起点和终点标记
            if (points.length > 0) {
                // 起点标记（绿色）
                const startMarker = new T.Marker(points[0], {
                    icon: new T.Icon({
                        iconUrl: 'data:image/svg+xml;base64,' + btoa(`
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                <circle cx="12" cy="12" r="8" fill="#27ae60" stroke="white" stroke-width="2"/>
                                <text x="12" y="16" text-anchor="middle" fill="white" font-size="10">起</text>
                            </svg>
                        `),
                        iconSize: new T.Point(24, 24),
                        iconAnchor: new T.Point(12, 12)
                    })
                });
                map.addOverLay(startMarker);
                markers.push(startMarker);

                // 终点标记（红色）
                if (points.length > 1) {
                    const endMarker = new T.Marker(points[points.length - 1], {
                        icon: new T.Icon({
                            iconUrl: 'data:image/svg+xml;base64,' + btoa(`
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                    <circle cx="12" cy="12" r="8" fill="#e74c3c" stroke="white" stroke-width="2"/>
                                    <text x="12" y="16" text-anchor="middle" fill="white" font-size="10">终</text>
                                </svg>
                            `),
                            iconSize: new T.Point(24, 24),
                            iconAnchor: new T.Point(12, 12)
                        })
                    });
                    map.addOverLay(endMarker);
                    markers.push(endMarker);
                }
            }

            // 调整地图视野以包含所有轨迹点
            const bounds = new T.LngLatBounds();
            points.forEach(point => bounds.extend(point));
            map.fitBounds(bounds);

            // 显示轨迹信息
            showTrackInfo();
        }

        // 播放轨迹
        function playTrack() {
            if (trackData.length === 0) return;

            isPlaying = true;
            document.getElementById('playBtn').disabled = true;
            document.getElementById('pauseBtn').disabled = false;

            // 创建移动标记
            if (!window.movingMarker) {
                window.movingMarker = new T.Marker(new T.LngLat(trackData[0].longitude, trackData[0].latitude), {
                    icon: new T.Icon({
                        iconUrl: 'data:image/svg+xml;base64,' + btoa(`
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24">
                                <circle cx="12" cy="12" r="6" fill="#f39c12" stroke="white" stroke-width="2"/>
                            </svg>
                        `),
                        iconSize: new T.Point(20, 20),
                        iconAnchor: new T.Point(10, 10)
                    })
                });
                map.addOverLay(window.movingMarker);
            }

            // 开始播放动画
            playInterval = setInterval(() => {
                if (currentIndex < trackData.length) {
                    const point = trackData[currentIndex];
                    const lngLat = new T.LngLat(point.longitude, point.latitude);
                    window.movingMarker.setLngLat(lngLat);

                    // 更新信息面板
                    updateCurrentPointInfo(point);

                    currentIndex++;
                } else {
                    // 播放完成
                    pauseTrack();
                    showMessage('轨迹播放完成', 'success');
                }
            }, 1000); // 每秒移动一个点
        }

        // 暂停播放
        function pauseTrack() {
            isPlaying = false;
            if (playInterval) {
                clearInterval(playInterval);
                playInterval = null;
            }
            document.getElementById('playBtn').disabled = false;
            document.getElementById('pauseBtn').disabled = true;
        }

        // 重置轨迹
        function resetTrack() {
            pauseTrack();
            currentIndex = 0;
            if (window.movingMarker) {
                map.removeOverLay(window.movingMarker);
                window.movingMarker = null;
            }
            document.getElementById('playBtn').disabled = trackData.length === 0;
        }

        // 清除轨迹
        function clearTrack() {
            pauseTrack();
            currentIndex = 0;

            // 清除轨迹线
            if (trackLine) {
                map.removeOverLay(trackLine);
                trackLine = null;
            }

            // 清除标记
            markers.forEach(marker => map.removeOverLay(marker));
            markers = [];

            if (window.movingMarker) {
                map.removeOverLay(window.movingMarker);
                window.movingMarker = null;
            }

            // 隐藏信息面板
            document.getElementById('infoPanel').style.display = 'none';
            document.getElementById('playBtn').disabled = true;
        }

        // 显示轨迹信息
        function showTrackInfo() {
            if (trackData.length === 0) return;

            const infoPanel = document.getElementById('infoPanel');
            const trackInfo = document.getElementById('trackInfo');

            const startTime = trackData[0].time;
            const endTime = trackData[trackData.length - 1].time;
            const totalPoints = trackData.length;

            trackInfo.innerHTML = `
                <div class="info-item"><strong>轨迹点数:</strong> ${totalPoints}</div>
                <div class="info-item"><strong>开始时间:</strong> ${startTime}</div>
                <div class="info-item"><strong>结束时间:</strong> ${endTime}</div>
                <div class="info-item"><strong>当前位置:</strong> <span id="currentPosition">-</span></div>
                <div class="info-item"><strong>当前时间:</strong> <span id="currentTime">-</span></div>
            `;

            infoPanel.style.display = 'block';
        }

        // 更新当前点信息
        function updateCurrentPointInfo(point) {
            document.getElementById('currentPosition').textContent =
                `${point.longitude.toFixed(6)}, ${point.latitude.toFixed(6)}`;
            document.getElementById('currentTime').textContent = point.time;
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            const messageArea = document.getElementById('messageArea');
            const className = type === 'error' ? 'error-message' : 'success-message';
            messageArea.innerHTML = `<div class="${className}">${message}</div>`;

            // 3秒后自动清除消息
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 3000);
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            document.getElementById('loadingDiv').style.display = show ? 'block' : 'none';
        }

        // 页面加载完成后初始化地图
        window.onload = function() {
            initMap();
        };
    </script>
</body>
</html>
