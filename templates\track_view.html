<!DOCTYPE html>
<html>
<head>
    <title>设备定位展示</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: #f5f8fa;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 100%;
            height: 100vh;
            background: #fff;
            display: flex;
            flex-direction: column;
        }
        .header {
            background-color: #34495e;
            color: white;
            padding: 12px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .back-link {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            transition: background 0.3s;
        }
        .back-link:hover {
            background: rgba(255,255,255,0.3);
        }
        .control-panel {
            background: #fff;
            padding: 16px 24px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            gap: 16px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        .btn-primary {
            background: #3498db;
            color: white;
        }
        .btn-primary:hover {
            background: #2980b9;
        }
        .map-container {
            flex: 1;
            position: relative;
            overflow: hidden;
        }
        #mapDiv {
            width: 100%;
            height: 100%;
        }
        .info-panel {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255,255,255,0.95);
            padding: 12px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            min-width: 250px;
            z-index: 1000;
        }
        .info-panel h4 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 14px;
        }
        .info-item {
            margin-bottom: 6px;
            font-size: 13px;
            color: #666;
        }
        .error-message {
            color: #e74c3c;
            background: #fdf2f2;
            padding: 8px 12px;
            border-radius: 4px;
            border-left: 4px solid #e74c3c;
            margin: 8px 0;
        }
        .success-message {
            color: #27ae60;
            background: #f0f9f4;
            padding: 8px 12px;
            border-radius: 4px;
            border-left: 4px solid #27ae60;
            margin: 8px 0;
        }
        .no-map {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #666;
            font-size: 16px;
        }
    </style>
    <!-- 引入天地图API -->
    <script type="text/javascript" src="https://api.tianditu.gov.cn/api?v=4.0&tk=2269e63e4e238ea63aa27542dd2e54d4"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>设备定位展示</h1>
            <a class="back-link" href="/">← 返回主页</a>
        </div>

        <div class="control-panel">
            <button class="btn btn-primary" onclick="refreshLocation()">刷新位置</button>
            <span id="statusText">正在获取最新位置数据...</span>
            <div id="messageArea"></div>
        </div>

        <div class="map-container">
            <div id="mapDiv"></div>
            <div class="info-panel" id="infoPanel">
                <h4>最新位置信息</h4>
                <div id="deviceInfo">
                    <div class="info-item">状态: <span id="deviceStatus">等待数据</span></div>
                    <div class="info-item">经度: <span id="longitude">-</span></div>
                    <div class="info-item">纬度: <span id="latitude">-</span></div>
                    <div class="info-item">时间: <span id="updateTime">-</span></div>
                    <div class="info-item">GPS状态: <span id="gpsStatus">-</span></div>
                    <div class="info-item">ICCID: <span id="iccid">-</span></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let map;
        let marker;
        let updateInterval;

        // 初始化天地图
        function initMap() {
            try {
                // 创建地图实例
                map = new T.Map('mapDiv');

                // 设置地图中心点（中国中心位置）
                const center = new T.LngLat(116.40969, 39.89945);
                map.centerAndZoom(center, 10);

                // 添加地图类型控件
                const ctrl = new T.Control.MapType();
                map.addControl(ctrl);

                // 添加缩放控件
                const zoomCtrl = new T.Control.Zoom();
                map.addControl(zoomCtrl);

                console.log('天地图初始化成功');
                document.getElementById('statusText').textContent = '地图已加载，正在获取位置数据...';
            } catch (error) {
                console.error('天地图初始化失败:', error);
                document.getElementById('statusText').textContent = '地图加载失败，请检查网络连接';
            }
        }

        // 获取最新位置数据
        async function getLatestLocation() {
            try {
                const response = await fetch('/api/latest_location');
                const result = await response.json();

                if (result.status === 'success' && result.data) {
                    updateLocationDisplay(result.data);
                    updateMapLocation(result.data);
                    document.getElementById('statusText').textContent = '位置数据已更新';
                } else {
                    document.getElementById('statusText').textContent = '暂无位置数据';
                    clearLocationDisplay();
                }
            } catch (error) {
                console.error('获取位置数据失败:', error);
                document.getElementById('statusText').textContent = '获取位置数据失败';
                showMessage('获取位置数据失败', 'error');
            }
        }

        // 更新地图上的位置标记
        function updateMapLocation(data) {
            if (!map || !data.longitude || !data.latitude) return;

            try {
                // 创建位置点
                const lngLat = new T.LngLat(data.longitude, data.latitude);

                // 移除旧的标记
                if (marker) {
                    map.removeOverLay(marker);
                }

                // 创建新的标记
                marker = new T.Marker(lngLat, {
                    icon: new T.Icon({
                        iconUrl: 'data:image/svg+xml;base64,' + btoa(`
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24">
                                <circle cx="12" cy="12" r="8" fill="#e74c3c" stroke="white" stroke-width="2"/>
                                <circle cx="12" cy="12" r="3" fill="white"/>
                            </svg>
                        `),
                        iconSize: new T.Point(32, 32),
                        iconAnchor: new T.Point(16, 16)
                    })
                });

                // 添加标记到地图
                map.addOverLay(marker);

                // 设置地图中心到新位置
                map.centerAndZoom(lngLat, 15);

                // 添加信息窗口
                const infoWin = new T.InfoWindow();
                const infoContent = `
                    <div style="padding: 8px; font-size: 12px;">
                        <h4 style="margin: 0 0 8px 0;">设备位置</h4>
                        <p><strong>经度:</strong> ${data.longitude}</p>
                        <p><strong>纬度:</strong> ${data.latitude}</p>
                        <p><strong>时间:</strong> ${data.time}</p>
                        <p><strong>GPS状态:</strong> ${data.gps_status}</p>
                        <p><strong>ICCID:</strong> ${data.iccid}</p>
                    </div>
                `;
                infoWin.setContent(infoContent);
                infoWin.setLngLat(lngLat);
                map.addOverLay(infoWin);

                console.log('地图位置已更新:', data.longitude, data.latitude);
            } catch (error) {
                console.error('更新地图位置失败:', error);
            }
        }

        // 更新位置显示
        function updateLocationDisplay(data) {
            document.getElementById('deviceStatus').textContent = '在线';
            document.getElementById('longitude').textContent = data.longitude || '-';
            document.getElementById('latitude').textContent = data.latitude || '-';
            document.getElementById('updateTime').textContent = data.time || '-';
            document.getElementById('gpsStatus').textContent = data.gps_status || '-';
            document.getElementById('iccid').textContent = data.iccid || '-';
        }

        // 清除位置显示
        function clearLocationDisplay() {
            document.getElementById('deviceStatus').textContent = '离线';
            document.getElementById('longitude').textContent = '-';
            document.getElementById('latitude').textContent = '-';
            document.getElementById('updateTime').textContent = '-';
            document.getElementById('gpsStatus').textContent = '-';
            document.getElementById('iccid').textContent = '-';

            // 清除地图标记
            if (marker && map) {
                map.removeOverLay(marker);
                marker = null;
            }
        }

        // 手动刷新位置
        function refreshLocation() {
            document.getElementById('statusText').textContent = '正在刷新位置数据...';
            getLatestLocation();
        }

        // 启动自动更新
        function startAutoUpdate() {
            // 立即获取一次数据
            getLatestLocation();

            // 每5秒自动更新一次
            updateInterval = setInterval(getLatestLocation, 5000);
        }

        // 停止自动更新
        function stopAutoUpdate() {
            if (updateInterval) {
                clearInterval(updateInterval);
                updateInterval = null;
            }
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            const messageArea = document.getElementById('messageArea');
            const className = type === 'error' ? 'error-message' : 'success-message';
            messageArea.innerHTML = `<div class="${className}">${message}</div>`;

            // 3秒后自动清除消息
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 3000);
        }

        // 页面加载完成后初始化
        window.onload = function() {
            // 等待天地图API加载完成
            if (typeof T !== 'undefined') {
                initMap();
                startAutoUpdate();
            } else {
                // 如果API还没加载完成，等待一下再试
                setTimeout(function() {
                    if (typeof T !== 'undefined') {
                        initMap();
                        startAutoUpdate();
                    } else {
                        document.getElementById('statusText').textContent = '天地图API加载失败';
                        console.error('天地图API未能正确加载');
                    }
                }, 1000);
            }
        };

        // 页面关闭时停止自动更新
        window.addEventListener('beforeunload', function() {
            stopAutoUpdate();
        });
    </script>
</body>
</html>
