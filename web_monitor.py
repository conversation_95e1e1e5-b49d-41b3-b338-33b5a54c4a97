from flask import Flask, render_template, jsonify, request, Response
import threading
import logging
import datetime
import json
import time

app = Flask(__name__)

# 禁用Flask默认的访问日志
app.logger.disabled = True
logging.getLogger('werkzeug').disabled = True

# 用于存储最新的设备数据
device_data = []
# 用于存储原始数据
raw_data = []
# 最大保存记录数
MAX_RECORDS = 100
# 用于存储最近的日志以供显示
recent_logs = []
# 用于存储当前连接的设备
active_clients = {}
# TCP服务器引用
tcp_server = None
# 用于实时日志推送的客户端列表
log_clients = []

def add_log_handler():
    class QueueHandler(logging.Handler):
        def emit(self, record):
            # 扩展日志关键词，捕获更多操作
            keywords = [
                '收到数据', '新的连接', '发送配置', 'HB数据包含记录数',
                '设备连接开关', '客户端连接关闭', '向设备', '发送指令',
                '解析数据', '处理客户端', '收到AT命令', '收到TX调试信息',
                '时间设置指令', '间隔配置', '工作时间段配置', '拒绝连接',
                '配置已保存', '指令已发送', 'TCP服务器启动', '监听地址',
                'ACCEPT', 'OK', 'CONNECTION_DENIED', '数据长度', '解析',
                '启动', '初始化', '连接', '断开', '发送', '接收'
            ]

            message = record.getMessage()

            # 检查是否包含关键词
            if any(keyword in message for keyword in keywords):
                # 跳过一些过于冗长的解析数据日志
                if '解析数据:\n' in message:
                    return

                log_entry = {
                    'time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],  # 包含毫秒
                    'level': record.levelname,
                    'message': message
                }

                # 添加到recent_logs
                recent_logs.insert(0, log_entry)

                # 保持recent_logs大小
                if len(recent_logs) > 1000:  # 增加日志保存数量
                    recent_logs.pop()

                # 实时推送给所有连接的客户端
                push_log_to_clients(log_entry)

    # 添加队列处理器到根日志记录器
    logging.getLogger().addHandler(QueueHandler())

def push_log_to_clients(log_entry):
    """推送日志到所有连接的SSE客户端"""
    if log_clients:
        data = json.dumps(log_entry)
        # 复制列表以避免迭代时修改
        clients_copy = log_clients.copy()
        for client in clients_copy:
            try:
                client.put(f"data: {data}\n\n")
            except:
                # 如果客户端断开连接，从列表中移除
                if client in log_clients:
                    log_clients.remove(client)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/data_view')
def data_view():
    return render_template('data_view.html')

@app.route('/api/data')
def get_data():
    return jsonify(device_data)

@app.route('/api/logs')
def get_logs():
    # 返回最近的日志（用于初始加载）
    return jsonify(recent_logs[:100])  # 只返回最近100条

@app.route('/api/logs/stream')
def log_stream():
    """Server-Sent Events 端点，用于实时日志推送"""
    import queue

    def event_stream():
        # 为这个客户端创建一个队列
        client_queue = queue.Queue()
        log_clients.append(client_queue)

        try:
            while True:
                try:
                    # 等待新的日志消息
                    data = client_queue.get(timeout=30)  # 30秒超时
                    yield data
                except queue.Empty:
                    # 发送心跳保持连接
                    yield "data: {\"type\": \"heartbeat\"}\n\n"
        except GeneratorExit:
            # 客户端断开连接
            if client_queue in log_clients:
                log_clients.remove(client_queue)

    return Response(event_stream(), mimetype='text/event-stream',
                   headers={
                       'Cache-Control': 'no-cache',
                       'Connection': 'keep-alive',
                       'Access-Control-Allow-Origin': '*'
                   })

@app.route('/api/raw_data')
def get_raw_data():
    return jsonify(raw_data)

@app.route('/api/device_connection_switch', methods=['GET', 'POST'])
def device_connection_switch():
    """设备连接开关API"""
    if tcp_server is None:
        return jsonify({'status': 'error', 'message': 'TCP服务器未初始化'})

    if request.method == 'POST':
        # 设置开关状态
        enabled = request.json.get('enabled', True)
        tcp_server.set_device_connection_enabled(enabled)
        return jsonify({'status': 'success', 'enabled': enabled})
    else:
        # 获取开关状态
        enabled = tcp_server.get_device_connection_enabled()
        return jsonify({'status': 'success', 'enabled': enabled})

# 移除配置发送开关API

@app.route('/api/data_counts', methods=['GET'])
def get_data_counts():
    """获取数据计数API"""
    if tcp_server is None:
        return jsonify({'status': 'error', 'message': 'TCP服务器未初始化'})

    counts = tcp_server.get_data_counts()
    repeat_count = tcp_server.get_repeat_data_count() if tcp_server else 0
    counts['repeat_count'] = repeat_count
    return jsonify({'status': 'success', 'data': counts})

@app.route('/api/reset_data_counts', methods=['POST'])
def reset_data_counts():
    """清零数据计数API"""
    if tcp_server is None:
        return jsonify({'status': 'error', 'message': 'TCP服务器未初始化'})

    result = tcp_server.reset_data_counts()
    if result:
        return jsonify({'status': 'success', 'message': '数据计数器已清零'})
    else:
        return jsonify({'status': 'error', 'message': '清零失败'})

@app.route('/api/send_command', methods=['POST'])
def send_command():
    cmd = request.json.get('command')
    if not cmd:
        return jsonify({'status': 'error', 'message': '命令不能为空'})

    try:
        logging.info(f"收到配置请求: {cmd}")

        if tcp_server is None:
            logging.error("TCP服务器未初始化")
            return jsonify({'status': 'error', 'message': 'TCP服务器未初始化，请检查服务器状态'})

        # 去掉指令格式限制，允许自由输入
        # 检查是否是标准配置指令格式
        if cmd.startswith('ZL+') or cmd.startswith('M') or cmd.startswith('S') or 'TIME=' in cmd or ('F' in cmd and 'E' in cmd):
            # 使用原有的配置指令处理逻辑
            result = tcp_server.set_config_command(cmd)
            if result:
                logging.info(f"配置已保存: {cmd}，将在设备下次连接时发送")
                return jsonify({'status': 'success', 'message': '配置已保存，将在设备下次连接时发送'})
            else:
                logging.error(f"配置指令格式错误: {cmd}")
                return jsonify({'status': 'error', 'message': '配置格式不正确'})
        else:
            # 对于非标准格式的指令，直接发送到所有连接的设备
            if not active_clients:
                return jsonify({'status': 'error', 'message': '当前没有连接的设备'})

            sent_count = 0
            for address, client_socket in active_clients.items():
                try:
                    # 确保指令以\r\n结尾
                    command_to_send = cmd if cmd.endswith('\r\n') else cmd + '\r\n'
                    client_socket.send(command_to_send.encode())
                    logging.info(f"向设备 {address} 发送自定义指令: {cmd}")
                    sent_count += 1
                except Exception as e:
                    logging.error(f"向设备 {address} 发送指令失败: {str(e)}")

            if sent_count > 0:
                return jsonify({'status': 'success', 'message': f'指令已发送到 {sent_count} 个设备'})
            else:
                return jsonify({'status': 'error', 'message': '指令发送失败'})

    except Exception as e:
        logging.error(f"处理配置请求异常: {str(e)}")
        return jsonify({'status': 'error', 'message': f'配置更新失败: {str(e)}'})

@app.route('/api/repeat_data_count', methods=['GET'])
def get_repeat_data_count():
    if tcp_server is None:
        return jsonify({'status': 'error', 'message': 'TCP服务器未初始化'})
    return jsonify({'status': 'success', 'count': tcp_server.get_repeat_data_count()})

@app.route('/api/reset_repeat_data_count', methods=['POST'])
def reset_repeat_data_count():
    if tcp_server is None:
        return jsonify({'status': 'error', 'message': 'TCP服务器未初始化'})
    tcp_server.reset_repeat_data_count()
    return jsonify({'status': 'success', 'message': '重复数据计数器已清零'})

@app.route('/api/reset_all_data', methods=['POST'])
def reset_all_data():
    global device_data, raw_data
    device_data.clear()
    raw_data.clear()
    if tcp_server:
        tcp_server.reset_data_counts()
        tcp_server.reset_repeat_data_count()
    return jsonify({'status': 'success', 'message': '所有数据已清零'})

def add_device_data(data):
    device_data.insert(0, {
        'time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'data': data
    })
    # 保持最大记录数
    if len(device_data) > MAX_RECORDS:
        device_data.pop()

def add_raw_data(data):
    raw_data.insert(0, {
        'time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'data': data
    })
    # 保持最大记录数
    if len(raw_data) > MAX_RECORDS:
        raw_data.pop()

def init_tcp_server(server):
    global tcp_server
    tcp_server = server
    logging.info("TCP服务器引用初始化成功")

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
